/**
 * <AUTHOR> <<EMAIL>> (http://piotrwitek.github.io)
 * @copyright Copyright (c) 2016 Piotr Witek
 * @license MIT
 */
export { $Call, $Diff, $ElementType, $Keys, $NonMaybeType, $PropertyType, $ReadOnly, $Shape, $Values, Class, } from './utility-types';
export { Assign, Brand, DeepNonNullable, DeepPartial, DeepReadonly, DeepRequired, Diff, FunctionKeys, Intersection, Mutable, MutableKeys, NonFunctionKeys, NonUndefined, Omit, OmitByValue, OmitByValueExact, OptionalKeys, Overwrite, Optional, PickByValue, PickByValueExact, PromiseType, ReadonlyKeys, AugmentedRequired as Required, Required<PERSON><PERSON><PERSON>, SetComplement, SetDifference, SetIntersection, Subtract, SymmetricDifference, Unionize, UnionToIntersection, ValuesType, Writable, WritableKeys, } from './mapped-types';
export { Falsy, Falsy as Falsey, // deprecated in v3, backward compatibility until v4
isFalsy, Nullish, isNullish, Primitive, isPrimitive, } from './aliases-and-guards';
export { getReturnOfExpression } from './functional-helpers';
