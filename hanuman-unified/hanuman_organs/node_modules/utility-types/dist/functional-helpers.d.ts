/**
 * @function getReturnOfExpression
 * @deprecated from TS v2.8 use built-in ReturnType<T> or $Call API
 * @description infer the return type from a given "expression" (at runtime it's equivalent of "noop")
 * @template RT - ReturnType
 * @param expression: (...params: any[]) => RT
 * @returns undefined as RT
 */
export declare function getReturnOfExpression<RT>(expression: (...params: any[]) => RT): RT;
