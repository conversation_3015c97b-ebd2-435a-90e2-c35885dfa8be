{"name": "unist-util-is", "version": "4.1.0", "description": "unist utility to check if a node passes a test", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "tree", "node", "is", "equal", "check", "test", "type"], "repository": "syntax-tree/unist-util-is", "bugs": "https://github.com/syntax-tree/unist-util-is/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://github.com/Roang-zero1)"], "files": ["index.js", "convert.js", "index.d.ts", "convert.d.ts"], "types": "index.d.ts", "devDependencies": {"@types/mdast": "^3.0.0", "browserify": "^17.0.0", "dtslint": "^4.0.0", "fast-check": "^2.0.0", "lodash": "^4.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "tinyify": "^3.0.0", "unified": "^9.0.0", "xo": "^0.38.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "build-bundle": "browserify . -s unistUtilIs -o unist-util-is.js", "build-mangle": "browserify . -s unistUtilIs -o unist-util-is.min.js -p tinyify", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test", "test-types": "dtslint .", "test": "npm run format && npm run build && npm run test-coverage && npm run test-types"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"@typescript-eslint/no-unused-expressions": "off", "eqeqeq": ["error", "always", {"null": "ignore"}], "max-params": "off", "no-eq-null": "off", "unicorn/prefer-number-properties": "off", "unicorn/prefer-reflect-apply": "off", "unicorn/prefer-type-error": "off"}, "ignore": ["*.ts", "unist-util-is.js"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}}