{"name": "use-resize-observer", "version": "9.1.0", "main": "dist/bundle.cjs.js", "module": "dist/bundle.esm.js", "types": "dist/index.d.ts", "sideEffects": false, "repository": "**************:ZeeCoder/use-resize-observer.git", "description": "A React hook that allows you to use a ResizeObserver to measure an element's size.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["react", "hook", "react hook", "resize observer", "resize observer hook", "resize observer react hook", "use-resize-observer", "useresizeobserver", "resize hook", "size hook", "container query", "css in js", "measure", "size", "resize"], "scripts": {"build": "rollup -c && tsc && rm -rf dist/utils && cp dist/index.d.ts polyfilled.d.ts", "watch": "KARMA_BROWSERS=Chrome run-p 'src:watch' 'karma:watch'", "src:watch": "rollup -c -w", "check:size": "size-limit", "check:types": "tsc -p tests", "check:lint": "eslint src/**", "test": "run-s 'build' 'check:size' 'check:types' 'check:lint' 'test:unit' 'test:create:ssr' 'test:headless:chrome'", "test:unit": "jest", "test:create:ssr": "node ./tests/ssr/create-ssr-test.js", "test:chrome": "KARMA_BROWSERS=Chrome yarn karma:run", "test:headless:chrome": "KARMA_BROWSERS=ChromeHeadless yarn karma:run", "test:firefox": "KARMA_BROWSERS=Firefox yarn karma:run", "test:headless:firefox": "KARMA_BROWSERS=FirefoxHeadless yarn karma:run", "karma:run": "karma start --singleRun", "karma:watch": "KARMA_BROWSERS=Chrome karma start", "prepublish": "yarn build", "test:bs:all": "run-s 'test:bs:modern' 'test:bs:legacy'", "test:bs:modern": "KARMA_BROWSERS=modern yarn karma:run", "test:bs:legacy": "KARMA_BROWSERS=legacy yarn karma:run", "test:bs:chrome": "KARMA_BROWSERS=bs_chrome_latest yarn karma:run", "test:bs:firefox": "KARMA_BROWSERS=bs_firefox_latest yarn karma:run", "test:bs:safari": "KARMA_BROWSERS=bs_safari_13 yarn karma:run", "test:bs:edge": "KARMA_BROWSERS=bs_edge_latest yarn karma:run", "test:bs:opera": "KARMA_BROWSERS=bs_opera_latest yarn karma:run", "test:bs:ie": "KARMA_BROWSERS=bs_ie_11 yarn karma:run", "test:bs:ios_11": "KARMA_BROWSERS=bs_ios_11 yarn karma:run", "test:bs:ios_14": "KARMA_BROWSERS=bs_ios_14 yarn karma:run", "test:bs:samsung": "KARMA_BROWSERS=bs_samsung yarn karma:run", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,md}": ["prettier --write"]}, "files": ["dist/*", "polyfilled*", "README.md", "LICENSE", "CHANGELOG*", "CONTRIBUTING.md", "package.json"], "peerDependencies": {"react": "16.8.0 - 18", "react-dom": "16.8.0 - 18"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.7.7", "@babel/preset-react": "^7.9.4", "@babel/preset-typescript": "^7.9.0", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-inject": "^4.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "@semantic-release/changelog": "^6.0.1", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.4", "@semantic-release/npm": "^9.0.1", "@semantic-release/release-notes-generator": "^10.0.3", "@size-limit/preset-small-lib": "^5.0.1", "@testing-library/react": "^13.1.1", "@types/jest": "^28.1.1", "@types/karma": "^6.3.1", "@types/karma-jasmine": "^4.0.1", "@types/react": "^18.0.8", "@types/react-dom": "^18.0.3", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "babel-loader": "^8.1.0", "delay": "^5.0.0", "eslint": "^8.17.0", "eslint-plugin-react-hooks": "^4.5.0", "husky": "^8.0.1", "jest": "^28.1.1", "jest-environment-jsdom": "^28.1.1", "karma": "^6.3.4", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.0.0", "karma-firefox-launcher": "^2.1.1", "karma-jasmine": "^4.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "^0.0.34", "karma-webpack": "^5.0.0", "lint-staged": "^13.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.0.4", "react": "^18.0.0", "react-app-polyfill": "^3.0.0", "react-dom": "^18.0.0", "rollup": "^2.6.1", "semantic-release": "^19.0.3", "size-limit": "^5.0.1", "ts-jest": "^28.0.4", "typescript": "^4.3.5", "webpack": "^4.1"}, "dependencies": {"@juggle/resize-observer": "^3.3.1"}}