{"version": 3, "file": "VignetteShader.cjs", "sources": ["../../src/shaders/VignetteShader.ts"], "sourcesContent": ["/**\n * Vignette shader\n * based on PaintEffect postprocess from ro.me\n * http://code.google.com/p/3-dreams-of-black/source/browse/deploy/js/effects/PaintEffect.js\n */\n\nexport const VignetteShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    offset: { value: 1.0 },\n    darkness: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float offset;\n    uniform float darkness;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    // Eskils vignette\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n    \tvec2 uv = ( vUv - vec2( 0.5 ) ) * vec2( offset );\n    \tgl_FragColor = vec4( mix( texel.rgb, vec3( 1.0 - darkness ), dot( uv, uv ) ), texel.a );\n\n    /*\n\t\t// alternative version from glfx.js\n\t\t// this one makes more \"dusty\" look (as opposed to \"burned\")\n\n\t\tvec4 color = texture2D( tDiffuse, vUv );\n\t\tfloat dist = distance( vUv, vec2( 0.5 ) );\n\t\tcolor.rgb *= smoothstep( 0.8, offset * 0.799, dist *( darkness + offset ) );\n\t\tgl_FragColor = color;\n\t\t*/\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAMO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,QAAQ,EAAE,OAAO,EAAI;AAAA,IACrB,UAAU,EAAE,OAAO,EAAI;AAAA,EACzB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4B7B;;"}