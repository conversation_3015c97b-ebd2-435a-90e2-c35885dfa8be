{"version": 3, "file": "PixelShader.cjs", "sources": ["../../src/shaders/PixelShader.ts"], "sourcesContent": ["/**\n * Pixelation shader\n */\n\nexport const PixelShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: null },\n    pixelSize: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying highp vec2 vUv;\n\n    void main() {\n\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float pixelSize;\n    uniform vec2 resolution;\n\n    varying highp vec2 vUv;\n\n    void main(){\n\n      vec2 dxy = pixelSize / resolution;\n      vec2 coord = dxy * floor( vUv / dxy );\n      gl_FragColor = texture2D(tDiffuse, coord);\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAIO,MAAM,cAAc;AAAA,EACzB,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,EAAE;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B;;"}